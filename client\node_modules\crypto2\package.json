{"name": "crypto2", "version": "2.0.0", "description": "crypto2 is a convenience wrapper around Node.js' crypto module.", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "main": "dist/crypto2.js", "dependencies": {"babel-runtime": "6.26.0", "node-rsa": "0.4.2", "util.promisify": "1.0.0"}, "devDependencies": {"assertthat": "1.0.0", "roboter": "1.0.2"}, "repository": {"type": "git", "url": "git://github.com/thenativeweb/crypto2.git"}, "keywords": ["cryptography", "encryption", "hash", "security"], "license": "MIT"}