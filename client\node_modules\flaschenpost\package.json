{"name": "flaschenpost", "version": "1.1.3", "description": "flaschenpost is a logger for cloud-based applications.", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jan-hend<PERSON>.g<PERSON><PERSON><PERSON><PERSON>@thenativeweb.io"}], "bin": {"flaschenpost-normalize": "dist/bin/flaschenpost-normalize.js", "flaschenpost-uncork": "dist/bin/flaschenpost-uncork.js"}, "main": "dist/flaschenpost.js", "dependencies": {"@babel/runtime": "7.2.0", "app-root-path": "2.1.0", "babel-runtime": "6.26.0", "chalk": "2.4.1", "find-root": "1.1.0", "lodash": "4.17.11", "moment": "2.22.2", "processenv": "1.1.0", "split2": "3.0.0", "stack-trace": "0.0.10", "stringify-object": "3.3.0", "untildify": "3.0.3", "util.promisify": "1.0.0", "varname": "2.0.3"}, "devDependencies": {"assertthat": "2.0.0", "express": "4.16.4", "fs-extra": "7.0.1", "morgan": "1.9.1", "nodeenv": "1.0.0", "roboter": "3.0.3", "shelljs": "0.8.3", "sinon": "7.1.1", "strip-ansi": "5.0.0", "supertest": "3.3.0"}, "repository": {"type": "git", "url": "git://github.com/thenativeweb/flaschenpost.git"}, "keywords": ["log"], "license": "MIT"}