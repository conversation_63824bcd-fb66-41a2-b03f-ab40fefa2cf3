{"name": "cookie", "description": "HTTP server cookie parsing and serialization", "version": "0.3.1", "author": "<PERSON>ylman <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "keywords": ["cookie", "cookies"], "repository": "jshttp/cookie", "devDependencies": {"istanbul": "0.4.3", "mocha": "1.21.5"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}}