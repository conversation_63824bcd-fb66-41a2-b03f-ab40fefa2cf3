{"name": "json-lines", "version": "1.0.0", "description": "json-lines streams JSON Lines.", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jan-hend<PERSON>.g<PERSON><PERSON><PERSON><PERSON>@thenativeweb.io"}], "main": "dist/jsonLines.js", "dependencies": {"timer2": "1.0.0"}, "devDependencies": {"assertthat": "1.0.0", "body-parser": "1.18.2", "express": "4.16.2", "roboter": "0.15.6", "roboter-server": "0.15.6"}, "repository": {"type": "git", "url": "git://github.com/thenativeweb/json-lines.git"}, "keywords": ["json", "json-lines", "parser", "stream"], "license": "MIT"}