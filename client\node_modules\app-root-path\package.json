{"name": "app-root-path", "version": "2.1.0", "description": "Determine an app's root path from anywhere inside the app", "main": "index.js", "browser": "browser-shim.js", "scripts": {"test": "nyc mocha -R spec", "report-coverage": "npm test && nyc report --reporter=text-lcov > coverage.lcov && codecov", "release": "semantic-release pre && npm publish && semantic-release post"}, "repository": {"type": "git", "url": "https://github.com/inxilpro/node-app-root-path.git"}, "keywords": ["root", "path", "utility", "util", "node", "module", "modules", "node_modules", "require", "app"], "author": "<PERSON> <http://cmorrell.com>", "license": "MIT", "bugs": {"url": "https://github.com/inxilpro/node-app-root-path/issues"}, "homepage": "https://github.com/inxilpro/node-app-root-path", "devDependencies": {"codecov": "^1.0.1", "coveralls": "^2.11.2", "cracks": "^3.1.2", "cz-conventional-changelog": "^1.2.0", "ghooks": "^1.3.2", "istanbul": "^0.3.4", "mocha": "^2.0.1", "mocha-lcov-reporter": "0.0.1", "mockery": "^1.7.0", "nyc": "^8.1.0", "semantic-release": "^4.3.5", "validate-commit-msg": "^2.8.0"}, "engines": {"node": ">= 4.0.0"}, "release": {"branch": "master"}, "config": {"ghooks": {"commit-msg": "validate-commit-msg", "post-merge": "npm install", "post-rewrite": "npm install"}, "commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}