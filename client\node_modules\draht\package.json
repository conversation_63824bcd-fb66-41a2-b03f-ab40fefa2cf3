{"name": "draht", "version": "1.0.1", "description": "draht provides process-level messaging.", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "main": "dist/Draht.js", "dependencies": {"eventemitter2": "5.0.1"}, "devDependencies": {"assertthat": "1.0.0", "roboter": "0.15.6", "roboter-server": "0.15.6"}, "repository": {"type": "git", "url": "git://github.com/thenativeweb/draht.git"}, "keywords": ["eventbus", "messaging"], "license": "MIT"}