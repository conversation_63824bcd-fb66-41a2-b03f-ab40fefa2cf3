{"name": "uuidv4", "version": "2.0.0", "description": "uuid creates UUIDs.", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jan-hend<PERSON>.g<PERSON><PERSON><PERSON><PERSON>@thenativeweb.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "main": "dist/uuidv4.js", "dependencies": {"sha-1": "0.1.1", "uuid": "3.3.2"}, "devDependencies": {"assertthat": "1.0.0", "roboter": "1.0.7"}, "repository": {"type": "git", "url": "git://github.com/thenativeweb/uuidv4.git"}, "keywords": ["guid", "uuid"], "license": "MIT"}